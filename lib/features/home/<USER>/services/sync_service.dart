import 'package:flutter/foundation.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/repositories/home_repository.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_tasks_usecase.dart';

class SyncService {
  SyncService._internal();

  static final SyncService _instance = SyncService._internal();

  factory SyncService() => _instance;

  final ValueNotifier<bool> isSyncing = ValueNotifier(false);

  Future<void> sync() async {
    if (isSyncing.value) return;

    try {
      isSyncing.value = true;

      // Run basic sync operations in parallel
      await Future.wait([
        _syncTasks(),
        _syncCalendar(),
      ]);

      // Sequential photo and signature synchronization
      await _syncPhotosAndSignatures();
    } finally {
      isSyncing.value = false;
    }
  }

  Future<void> _syncTasks() async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = await sl<DataManager>().getUserId() ?? '';
      const String actualDeviceUid = "8b7a6774c878a206";
      const String actualAppVersion = "9.9.9";

      final request = TasksRequestEntity(
        deviceUid: actualDeviceUid,
        userId: id,
        appversion: actualAppVersion,
        tasks: const [],
        token: token,
      );

      await sl<GetTasksUseCase>().call(request, isSync: true);

      // The repository now handles caching, so no need to save here.
      // if (result.isSuccess && result.data != null) {
      //   await sl<HomeLocalDataSource>().saveTasks(result.data!);
      // }
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  Future<void> _syncCalendar() async {
    try {
      final token = await sl<DataManager>().getAuthToken() ?? '';
      final id = await sl<DataManager>().getUserId() ?? '';

      final params = GetCalendarParams(token: token, userId: id);
      await sl<GetCalendarUseCase>().call(params, isSync: true);
    } catch (e) {
      // Handle or log error appropriately
    }
  }

  /// Sequential photo and signature synchronization
  ///
  /// This method performs the following operations in sequence:
  /// 1. Upload photos API endpoint
  /// 2. Sync photos API endpoint (after successful photo upload)
  /// 3. Upload signatures API endpoint
  /// 4. Sync signatures API endpoint (after successful signature upload)
  Future<void> _syncPhotosAndSignatures() async {
    try {
      // Get all tasks from local database
      final tasks = await _getAllTasksFromDatabase();

      if (tasks.isEmpty) {
        logger('No tasks found for photo and signature sync');
        return;
      }

      // Step 1 & 2: Upload and sync photos
      logger('🚀 Starting photo upload and sync workflow');
      final photoSyncResult = await sl<HomeRepository>().uploadAndSyncPhotos(
        tasks: tasks,
      );

      if (photoSyncResult.isSuccess) {
        logger('✅ Photo upload and sync completed successfully');
      } else {
        logger('❌ Photo upload and sync failed: ${photoSyncResult.error}');
      }

      // Step 3 & 4: Upload and sync signatures
      logger('🚀 Starting signature upload and sync workflow');
      final signatureSyncResult =
          await sl<HomeRepository>().uploadAndSyncSignatures(
        tasks: tasks,
      );

      if (signatureSyncResult.isSuccess) {
        logger('✅ Signature upload and sync completed successfully');
      } else {
        logger(
            '❌ Signature upload and sync failed: ${signatureSyncResult.error}');
      }
    } catch (e) {
      logger('❌ Error during photo and signature sync: $e');
    }
  }

  /// Get all tasks from the local database
  Future<List<TaskDetail>> _getAllTasksFromDatabase() async {
    try {
      final realm = sl<RealmDatabase>().realm;
      final taskModels = realm.all<TaskDetailModel>();

      // Convert models to entities
      final tasks =
          taskModels.map((model) => TaskDetailMapper.toEntity(model)).toList();

      logger('Retrieved ${tasks.length} tasks from local database');
      return tasks;
    } catch (e) {
      logger('❌ Error retrieving tasks from database: $e');
      return [];
    }
  }
}
